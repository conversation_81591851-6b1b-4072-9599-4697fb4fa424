import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { login } from '@/services/AuthService'
import type { LoginRequest } from '@/types/auth'
import { mapLoginError } from '@/utils/ErrorMapper'

/**
 * Логика входа.
 */
export function useLogin() {
  const email = ref('')
  const password = ref('')
  const loading = ref(false)
  const errorMessage = ref<string | null>(null)

  const authStore = useAuthStore()
  const router = useRouter()

  /**
   * Обработчик отправки формы логина.
   *
   * Выполняет запрос авторизации, сохраняет сессию и перенаправляет пользователя.
   */
  const submit = async () => {
    loading.value = true
    errorMessage.value = null

    const payload: LoginRequest = {
      email: email.value,
      password: password.value,
    }

    try {
      const response = await login(payload)

      authStore.setSession(response)

      await router.push('/hello')
    } catch (err) {
      mapLoginError(err)
    } finally {
      loading.value = false
    }
  }

  return {
    email,
    password,
    submit,
    loading,
    errorMessage,
  }
}
