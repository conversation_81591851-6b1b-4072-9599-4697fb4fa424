import { ref } from 'vue'
import { useRouter } from 'vue-router'

/**
 * Логика регистрации.
 */
export function useRegister() {
  const firstName = ref('')
  const lastName = ref('')
  const email = ref('')
  const phone = ref('')
  const password = ref('')
  const loading = ref(false)
  const errorMessage = ref<string | null>(null)

  const router = useRouter()

  /**
   * Обработчик отправки формы регистрации.
   *
   * Выполняет запрос регистрации, перенаправляет пользователя на страницу авторизации.
   */
  const submit = async () => {
    console.log(firstName)
    console.log(lastName)
    console.log(email)
    console.log(phone)
    console.log(password)
    console.log(loading)
    console.log(errorMessage)

    await router.push('/login')
  }

  return {
    firstName,
    lastName,
    email,
    phone,
    password,
    loading,
    errorMessage,
    submit
  }
}

