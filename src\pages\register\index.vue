<script setup lang="ts">
import { useRegister } from '@/composables/useRegister'

const { firstName, lastName, email, phone, password, loading, errorMessage, submit } = useRegister()

/**
 * Обработчик отправки формы.
 *
 * @param e событие отправки формы
 */
const handleSubmit = async (e: Event) => {
  e.preventDefault()

  await submit()
}
</script>
<template>
  <div class="page-container">
    <main class="register-wrapper">
      <form class="register-form" @submit="handleSubmit">
        <h1 class="form-title">Регистрация</h1>

        <div class="form-group">
          <label for="firstName">Имя</label>
          <input
            id="firstName"
            type="text"
            v-model="firstName"
            placeholder="Имя"
            autocomplete="firstName"
            required
          />
        </div>

        <div class="form-group">
          <label for="lastName">Фамилия</label>
          <input
            id="lastName"
            type="text"
            v-model="lastName"
            placeholder="Фамилия"
            autocomplete="lastName"
            required
          />
        </div>

        <div class="form-group">
          <label for="email">E-mail</label>
          <input
            id="email"
            type="email"
            v-model="email"
            placeholder="E-mail"
            autocomplete="email"
            required
          />
        </div>

        <div class="form-group">
          <label for="phone">Телефон</label>
          <input
            id="phone"
            type="phone"
            v-model="phone"
            placeholder="Phone"
            autocomplete="phone"
            required
          />
        </div>

        <div class="form-group">
          <label for="password">Пароль</label>
          <input
            id="password"
            type="password"
            v-model="password"
            placeholder="Password"
            autocomplete="current-password"
            required
          />
        </div>

        <div v-if="errorMessage" class="form-error">
          {{ errorMessage }}
        </div>

        <input
          class="submit-btn"
          type="submit"
          :disabled="loading"
          :value="loading ? 'Регистрация...' : 'Регистрация'"
        />
      </form>
    </main>
  </div>
</template>

<style scoped>
.page-container {
  display: flex;
  flex-direction: column;
  max-height: 100vh;
}

.register-wrapper {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.register-form {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 25rem;
  gap: 1.5rem;
  margin: 0 auto;
  padding: 2rem;
}

.form-title {
  font-size: 2.5rem;
  font-weight: bold;
  text-align: center;
}

.form-group {
  position: relative;
  padding-bottom: 0.1rem;
}

.form-group::after {
  content: '';
  position: absolute;
  left: 1rem;
  right: 1rem;
  bottom: 0;
  height: 1px;
  background: #ccc;
}

label {
  width: 6rem;
  text-align: center;
  font-size: 1rem;
  color: #27374d;
  margin-right: 1rem;
}

input {
  flex: 1;
  font-size: 1rem;
  padding: 0.5rem 0;
  border: none;
  outline: none;
  min-width: 19rem;
}

input::placeholder {
  color: #9db2bf;
}

.submit-btn {
  margin-top: 1rem;
  padding: 0.75rem;
  border: none;
  border-radius: 0.5rem;
  background-color: #222;
  color: #fff;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.submit-btn:hover {
  background-color: #393e46;
}
</style>
