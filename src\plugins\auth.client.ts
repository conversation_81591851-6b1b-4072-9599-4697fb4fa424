import { useAuthStore } from '@/stores/auth'
import { defineNuxtPlugin } from '#imports'
import type { LoginResponse } from '@/types/auth'
import { scheduleAutoRefresh } from '@/utils/AutoRefresh'
import { useApi } from '@/composables/useApi'

/**
 * Обновляет токен авторизации при входе на сайт и запускает автоматическое обновление токенов.
 */
export default defineNuxtPlugin(async () => {
  const authStore = useAuthStore()
  const api = useApi()

  try {
    const response = await api<LoginResponse>('/user-service/api/v1/public/refresh', {
      method: 'POST',
      credentials: 'include',
    })

    authStore.setSession(response)
    scheduleAutoRefresh()
  } catch (e) {
    console.warn('Не удалось востановить сессию: ', e)
    authStore.clearSession()

    // TODO: Сделать логику если:
    //  - Токен обновления протух
    //  - Отправить пользователя на страницу с логином с уведомлением что он вышел с аккаунта
  }
})
