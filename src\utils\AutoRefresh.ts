import { useAuthStore } from '@/stores/auth'
import { navigateTo } from '#imports'
import { useApi } from '@/composables/useApi'
import type { LoginResponse } from '@/types/auth'

let refreshTimeout: ReturnType<typeof setTimeout> | null = null
let logoutTimeout: ReturnType<typeof setTimeout> | null = null

export function scheduleAutoRefresh() {
  const auth = useAuthStore()

  if (!auth.accessTokenExpiresAt || !auth.refreshTokenExpiresAt) return

  const now = Date.now()
  const refreshIn = auth.accessTokenExpiresAt - now - 10_000
  const logoutIn = auth.refreshTokenExpiresAt - now

  if (refreshIn > 0) {
    if (refreshTimeout) clearTimeout(refreshTimeout)
    refreshTimeout = setTimeout(refreshToken, refreshIn)
  }

  if (logoutIn > 0) {
    if (logoutTimeout) clearTimeout(logoutTimeout)
    logoutTimeout = setTimeout(() => {
      auth.clearSession()
      navigateTo('/login')
    }, logoutIn)
  }
}

async function refreshToken() {
  const auth = useAuthStore()
  const api = useApi()

  try {
    const response = await api<LoginResponse>('/user-service/api/v1/public/refresh', {
      method: 'POST',
      credentials: 'include',
    })
    auth.setSession(response)
    scheduleAutoRefresh()
  } catch (e) {
    console.error('Ошибка при обновлении токенов:', e)
    auth.clearSession()
    navigateTo('/login')
  }
}
