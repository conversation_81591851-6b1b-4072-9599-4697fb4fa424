import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import Login from '@/pages/login/index.vue'
import { ref, type Ref } from 'vue'

let mockEmail: Ref<string>
let mockPassword: Ref<string>
let mockSubmit: ReturnType<typeof vi.fn>
let mockLoading: Ref<boolean>
let mockError: Ref<string | null>

vi.mock('@/composables/useLogin', () => ({
  useLogin: () => ({
    email: mockEmail,
    password: mockPassword,
    submit: mockSubmit,
    loading: mockLoading,
    errorMessage: mockError,
  }),
}))

describe('Login', () => {
  beforeEach(() => {
    mockEmail = ref('')
    mockPassword = ref('')
    mockSubmit = vi.fn()
    mockLoading = ref(false)
    mockError = ref(null)
  })

  it('По умолчанию отображает "Вход" в теге h1', () => {
    const wrapper = mount(Login, {
      global: {
        plugins: [createTestingPinia({ stubActions: false })],
      },
    })

    const h1 = wrapper.find('.form-title')
    expect(h1.text()).toBe('Вход')
  })

  it('По умолчанию отображает пустое значение в поле E-mail', () => {
    const wrapper = mount(Login, {
      global: {
        plugins: [createTestingPinia({ stubActions: false })],
      },
    })

    const emailInput = wrapper.get('input[type="email"]')
    expect((emailInput.element as HTMLInputElement).value).toBe('')
  })

  it('По умолчнию отображает пустое значение в поле password', () => {
    const wrapper = mount(Login, {
      global: {
        plugins: [createTestingPinia({ stubActions: false })],
      },
    })

    const passwordInput = wrapper.find('input[type="password"]')
    expect((passwordInput.element as HTMLInputElement).value).toBe('')
  })

  it('По умолчанию ошибка не показывается', () => {
    const wrapper = mount(Login, {
      global: {
        plugins: [createTestingPinia({ stubActions: false })],
      },
    })

    const formError = wrapper.find('.form-error')
    expect(formError.exists()).toBe(false)
  })

  it('По умолчанию кнопка имеет value "Вход"', () => {
    const wrapper = mount(Login, {
      global: {
        plugins: [createTestingPinia({ stubActions: false })],
      },
    })

    const submitBtn = wrapper.find('.submit-btn')
    expect((submitBtn.element as HTMLInputElement).value).toBe('Вход')
  })

  it('Обновляет email и password через v-model', async () => {
    const wrapper = mount(Login, {
      global: {
        plugins: [createTestingPinia({ stubActions: false })],
      },
    })

    const emailInput = wrapper.get('input#email')
    const passwordInput = wrapper.get('input#password')

    await emailInput.setValue('<EMAIL>')
    await passwordInput.setValue('123456789')

    expect(mockEmail.value).toBe('<EMAIL>')
    expect(mockPassword.value).toBe('123456789')
  })

  it('Отправляет форму и вызывает login', async () => {
    const wrapper = mount(Login, {
      global: {
        plugins: [createTestingPinia({ stubActions: false })],
      },
    })

    await wrapper.get('form').trigger('submit.prevent')

    expect(mockSubmit).toHaveBeenCalled()
  })

  it('Отображает сообщене об ошибке (400)', async () => {
    mockError.value = 'Неверный email или пароль.'
    const wrapper = mount(Login, {
      global: {
        plugins: [createTestingPinia({ stubActions: false })],
      },
    })

    expect(wrapper.text()).toContain('Неверный email или пароль.')
  })

  it('Отображает сообщене об ошибке (404)', async () => {
    mockError.value = 'Пользователь не найден или заблокирован.'
    const wrapper = mount(Login, {
      global: {
        plugins: [createTestingPinia({ stubActions: false })],
      },
    })

    expect(wrapper.text()).toContain('Пользователь не найден или заблокирован.')
  })

  it('Отключение кнопки входа при загрузке', async () => {
    mockLoading.value = true
    const wrapper = mount(Login, {
      global: {
        plugins: [createTestingPinia({ stubActions: false })],
      },
    })

    const submitButton = wrapper.get('input.submit-btn')
    expect(submitButton.attributes('disabled')).toBeDefined()
    expect((submitButton.element as HTMLInputElement).value).toBe('Вход...')
  })
})
